Synopsis

    cordova-cli telemetry [STATE]
    
      STATE: on|off
    
Turns telemetry collection on or off
    
    on ....................... Turns telemetry collection on
    off ...................... Turns telemetry collection off  

Details
    A timed prompt asking the user to opt-in or out is displayed the first time cordova is run.
    It lasts for 30 seconds, after which the user is automatically opted-out if they don't provide any answer.
    In CI environments, the `CI` environment variable can be set, which will prevent the prompt from showing up.
    Telemetry collection can also be turned off on a single command by using the `--no-telemetry` flag.

Examples
    cordova-cli telemetry on
    cordova-cli telemetry off
    cordova-cli build --no-telemetry

For details, see our privacy notice: https://cordova.apache.org/privacy