{"name": "cordova-serve", "version": "4.0.1", "description": "Apache Cordova server support for cordova-lib and cordova-browser.", "main": "src/main.js", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run cover", "test:unit": "jasmine \"spec/**/*.spec.js\"", "cover": "nyc npm run test:unit"}, "repository": "github:apache/cordova-serve", "bugs": "https://github.com/apache/cordova-serve/issues", "keywords": ["<PERSON><PERSON>", "server", "apache"], "author": "Apache Software Foundation", "license": "Apache-2.0", "dependencies": {"chalk": "^3.0.0", "compression": "^1.7.4", "express": "^4.17.1", "open": "^7.0.3", "which": "^2.0.2"}, "devDependencies": {"@cordova/eslint-config": "^3.0.0", "jasmine": "^3.5.0", "nyc": "^15.0.0", "rewire": "^5.0.0"}, "engines": {"node": ">= 10", "npm": ">= 5.6.0"}, "nyc": {"all": true, "exclude": ["coverage/", "spec/"], "reporter": ["lcov", "text"]}}