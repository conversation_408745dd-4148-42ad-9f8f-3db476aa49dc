{"name": "@npmcli/promise-spawn", "version": "1.3.2", "files": ["index.js"], "description": "spawn processes the way the npm cli likes to do", "repository": {"type": "git", "url": "git+https://github.com/npm/promise-spawn"}, "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "devDependencies": {"minipass": "^3.1.1", "require-inject": "^1.4.4", "tap": "^14.10.6"}, "dependencies": {"infer-owner": "^1.0.4"}}