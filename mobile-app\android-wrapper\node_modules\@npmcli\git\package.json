{"name": "@npmcli/git", "version": "2.1.0", "main": "lib/index.js", "files": ["lib/*.js"], "description": "a util for spawning git from npm CLI contexts", "repository": {"type": "git", "url": "git+https://github.com/npm/git"}, "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"lint": "standard", "lint:fix": "standard --fix", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "snap": "tap", "test": "tap", "posttest": "npm run lint"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "devDependencies": {"slash": "^3.0.0", "standard": "^16.0.3", "tap": "^15.0.6"}, "dependencies": {"@npmcli/promise-spawn": "^1.3.2", "lru-cache": "^6.0.0", "mkdirp": "^1.0.4", "npm-pick-manifest": "^6.1.1", "promise-inflight": "^1.0.1", "promise-retry": "^2.0.1", "semver": "^7.3.5", "which": "^2.0.2"}}