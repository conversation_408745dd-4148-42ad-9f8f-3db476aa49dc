<?xml version="1.0" encoding="UTF-8"?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

 http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
-->
<widget xmlns="http://www.w3.org/ns/widgets"
    xmlns:cdv="http://cordova.apache.org/ns/1.0"
    id="io.cordova.hellocordova"
    version="1.0.0">
    <name>HelloCordova</name>
    <description>Sample Apache Cordova App</description>
    <author email="<EMAIL>" href="https://cordova.apache.org">
        Apache Cordova Team
    </author>

    <!-- https://s.apache.org/cdv-content-config -->
    <content src="index.html" />

    <!-- To allow connections to other resources, you must explicitly permit them using `access` tags. -->
    <!-- https://s.apache.org/cdv-network-request-access -->
    <!-- Example:
    <access allow="https://cordova.apache.org" />
    -->

    <!-- To control which URLs the WebView itself can be navigated to, use the `allow-navigation` tags. -->
    <!-- https://s.apache.org/cdv-allow-navigation -->
    <!-- Example:
    <allow-navigation href="https://cordova.apache.org/*" />
    -->

    <!-- To control which URLs the app is allowed to ask the system to open, use the `allow-intent` tags. -->
    <!-- https://s.apache.org/cdv-allow-intent -->
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
</widget>
