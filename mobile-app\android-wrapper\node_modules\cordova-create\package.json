{"name": "cordova-create", "version": "4.1.0", "description": "Apache Cordova create module. Creates new project from default or template", "main": "index.js", "repository": "github:apache/cordova-create", "bugs": "https://github.com/apache/cordova-create/issues", "keywords": ["<PERSON><PERSON>", "create", "apache", "ecosystem:cordova", "cordova:tool"], "author": "Apache Software Foundation", "license": "Apache-2.0", "engines": {"node": ">=10"}, "dependencies": {"cordova-app-hello-world": "^6.0.0", "cordova-common": "^4.1.0", "cordova-fetch": "^3.1.0", "fs-extra": "^10.1.0", "globby": "^11.1.0", "import-fresh": "^3.3.0", "isobject": "^4.0.0", "npm-package-arg": "^8.1.5", "path-is-inside": "^1.0.2", "tmp": "^0.2.1", "valid-identifier": "0.0.2"}, "devDependencies": {"@cordova/eslint-config": "^4.0.0", "jasmine": "^3.99.0", "nyc": "^15.1.0", "rewire": "^5.0.0"}, "scripts": {"test": "npm run lint && npm run cover", "lint": "eslint .", "jasmine": "jasmine \"spec/**/*.spec.js\"", "cover": "nyc npm run jasmine"}, "nyc": {"include": ["index.js"], "reporter": ["lcov", "text"]}}