{"ios": {"hostos": ["darwin"], "url": "https://github.com/apache/cordova-ios.git", "version": "^6.2.0", "deprecated": false}, "osx": {"hostos": ["darwin"], "url": "https://github.com/apache/cordova-osx.git", "version": "^6.0.0", "deprecated": true}, "android": {"url": "https://github.com/apache/cordova-android.git", "version": "^10.1.1", "deprecated": false}, "windows": {"hostos": ["win32"], "url": "https://github.com/apache/cordova-windows.git", "version": "^7.0.0", "deprecated": true}, "browser": {"parser_file": "../cordova/metadata/browser_parser", "handler_file": "../plugman/platforms/browser", "url": "https://github.com/apache/cordova-browser.git", "version": "^6.0.0", "deprecated": false}, "electron": {"url": "https://github.com/apache/cordova-electron.git", "version": "^3.0.0", "deprecated": false}}