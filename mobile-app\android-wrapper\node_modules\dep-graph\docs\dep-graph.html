<!DOCTYPE html>  <html> <head>   <title>dep-graph.coffee</title>   <meta http-equiv="content-type" content="text/html; charset=UTF-8">   <link rel="stylesheet" media="all" href="docco.css" /> </head> <body>   <div id="container">     <div id="background"></div>          <table cellpadding="0" cellspacing="0">       <thead>         <tr>           <th class="docs">             <h1>               dep-graph.coffee             </h1>           </th>           <th class="code">           </th>         </tr>       </thead>       <tbody>                               <tr id="section-1">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-1">&#182;</a>               </div>               <p><a href="http://github.com/TrevorBurnham/dep-graph">dep-graph</a></p>             </td>             <td class="code">               <div class="highlight"><pre><span class="nv">_ = </span><span class="nx">require</span> <span class="s1">&#39;underscore&#39;</span>

<span class="k">class</span> <span class="nx">DepGraph</span>
  <span class="nv">constructor: </span><span class="o">-&gt;</span></pre></div>             </td>           </tr>                               <tr id="section-2">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-2">&#182;</a>               </div>               <p>The internal representation of the dependency graph in the format
<code>id: [ids]</code>, indicating only <em>direct</em> dependencies.</p>             </td>             <td class="code">               <div class="highlight"><pre>    <span class="vi">@map = </span><span class="p">{}</span></pre></div>             </td>           </tr>                               <tr id="section-3">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-3">&#182;</a>               </div>               <p>Add a direct dependency. Returns <code>false</code> if that dependency is a duplicate.</p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="nv">add: </span><span class="nf">(id, depId) -&gt;</span>
    <span class="nx">@map</span><span class="p">[</span><span class="nx">id</span><span class="p">]</span> <span class="o">?=</span> <span class="p">[]</span>
    <span class="k">return</span> <span class="kc">false</span> <span class="k">if</span> <span class="nx">depId</span> <span class="k">in</span> <span class="nx">@map</span><span class="p">[</span><span class="nx">id</span><span class="p">]</span>
    <span class="nx">@map</span><span class="p">[</span><span class="nx">id</span><span class="p">].</span><span class="nx">push</span> <span class="nx">depId</span>
    <span class="nx">@map</span><span class="p">[</span><span class="nx">id</span><span class="p">]</span></pre></div>             </td>           </tr>                               <tr id="section-4">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-4">&#182;</a>               </div>               <p>Generate a list of all dependencies (direct and indirect) for the given id,
in logical order with no duplicates.</p>             </td>             <td class="code">               <div class="highlight"><pre>  <span class="nv">getChain: </span><span class="nf">(id) -&gt;</span></pre></div>             </td>           </tr>                               <tr id="section-5">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-5">&#182;</a>               </div>               <p>First, get a list of all dependencies (unordered)</p>             </td>             <td class="code">               <div class="highlight"><pre>    <span class="nv">deps = </span><span class="nx">@descendantsOf</span> <span class="nx">id</span></pre></div>             </td>           </tr>                               <tr id="section-6">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-6">&#182;</a>               </div>               <p>Second, order them (using the Tarjan algorithm)</p>             </td>             <td class="code">               <div class="highlight"><pre>    <span class="nv">chain = </span><span class="p">[]</span>
    <span class="nv">visited = </span><span class="p">{}</span>
    <span class="nv">visit = </span><span class="p">(</span><span class="nx">node</span><span class="p">)</span> <span class="o">=&gt;</span>
      <span class="k">return</span> <span class="k">if</span> <span class="nx">visited</span><span class="p">[</span><span class="nx">node</span><span class="p">]</span> <span class="o">or</span> <span class="nx">node</span> <span class="o">is</span> <span class="nx">id</span>
      <span class="nx">visited</span><span class="p">[</span><span class="nx">node</span><span class="p">]</span> <span class="o">=</span> <span class="kc">true</span>
      <span class="k">for</span> <span class="nx">parent</span> <span class="k">in</span> <span class="nx">@parentsOf</span><span class="p">(</span><span class="nx">node</span><span class="p">)</span>
        <span class="nx">visit</span> <span class="nx">parent</span>
      <span class="nx">chain</span><span class="p">.</span><span class="nx">unshift</span> <span class="nx">node</span>

    <span class="k">for</span> <span class="nx">leafNode</span> <span class="k">in</span> <span class="nx">_</span><span class="p">.</span><span class="nx">intersection</span><span class="p">(</span><span class="nx">deps</span><span class="p">,</span> <span class="nx">@leafNodes</span><span class="p">()).</span><span class="nx">reverse</span><span class="p">()</span>
      <span class="nx">visit</span> <span class="nx">leafNode</span>

    <span class="nx">chain</span>

  <span class="nv">leafNodes: </span><span class="o">-&gt;</span>
    <span class="nv">allNodes = </span><span class="nx">_</span><span class="p">.</span><span class="nx">uniq</span> <span class="nx">_</span><span class="p">.</span><span class="nx">flatten</span> <span class="nx">_</span><span class="p">.</span><span class="nx">values</span> <span class="nx">@map</span>
    <span class="nx">node</span> <span class="k">for</span> <span class="nx">node</span> <span class="k">in</span> <span class="nx">allNodes</span> <span class="k">when</span> <span class="o">!</span><span class="nx">@map</span><span class="p">[</span><span class="nx">node</span><span class="p">]</span><span class="o">?</span><span class="p">.</span><span class="nx">length</span>

  <span class="nv">parentsOf: </span><span class="nf">(child) -&gt;</span>
    <span class="nx">node</span> <span class="k">for</span> <span class="nx">node</span> <span class="k">in</span> <span class="nx">_</span><span class="p">.</span><span class="nx">keys</span><span class="p">(</span><span class="nx">@map</span><span class="p">)</span> <span class="k">when</span> <span class="nx">child</span> <span class="k">in</span> <span class="nx">@map</span><span class="p">[</span><span class="nx">node</span><span class="p">]</span>

  <span class="nv">descendantsOf: </span><span class="nf">(parent, descendants = [], branch = []) -&gt;</span>
    <span class="nx">descendants</span><span class="p">.</span><span class="nx">push</span> <span class="nx">parent</span>
    <span class="nx">branch</span><span class="p">.</span><span class="nx">push</span> <span class="nx">parent</span>
    <span class="k">for</span> <span class="nx">child</span> <span class="k">in</span> <span class="nx">@map</span><span class="p">[</span><span class="nx">parent</span><span class="p">]</span> <span class="o">?</span> <span class="p">[]</span>
      <span class="k">if</span> <span class="nx">child</span> <span class="k">in</span> <span class="nx">branch</span>                <span class="c1"># cycle</span>
        <span class="k">throw</span> <span class="k">new</span> <span class="nb">Error</span><span class="p">(</span><span class="s2">&quot;Cyclic dependency from #{parent} to #{child}&quot;</span><span class="p">)</span>
      <span class="k">continue</span> <span class="k">if</span> <span class="nx">child</span> <span class="k">in</span> <span class="nx">descendants</span>  <span class="c1"># duplicate</span>
      <span class="nx">@descendantsOf</span> <span class="nx">child</span><span class="p">,</span> <span class="nx">descendants</span><span class="p">,</span> <span class="nx">branch</span><span class="p">.</span><span class="nx">slice</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
    <span class="nx">descendants</span><span class="p">[</span><span class="mi">1</span><span class="p">..]</span></pre></div>             </td>           </tr>                               <tr id="section-7">             <td class="docs">               <div class="pilwrap">                 <a class="pilcrow" href="#section-7">&#182;</a>               </div>               <p>Export the class in Node, make it global in the browser.</p>             </td>             <td class="code">               <div class="highlight"><pre><span class="k">if</span> <span class="nx">module</span><span class="o">?</span><span class="p">.</span><span class="nx">exports</span><span class="o">?</span>
  <span class="nv">module.exports = </span><span class="nx">DepGraph</span>
<span class="k">else</span>
  <span class="vi">@DepGraph = </span><span class="nx">DepGraph</span>

</pre></div>             </td>           </tr>                </tbody>     </table>   </div> </body> </html> 