Synopsis

    cordova-cli run [MODE] [BUILDOPTS] [TARGET] [PLATS] [BUILDCONFIG] [-- POPTS]

      MODE:        --list|--debug|--release
      BUILDOPTS:   --noprepare --nobuild
      TARGET:      DEVICECLASS|--target=FOO
      PLATS:       PLATFORM [...]
      BUILDCONFIG: --buildConfig=CONFIGFILE
      POPTS:       platformopts
      DEVICECLASS: --device|--emulator

Deploys app on specified platform devices / emulators

    --list ............................. Lists available targets
                                         Will display both device and emulator
                                         unless DEVICECLASS option is provided

    --debug ............................ Deploy a debug build
    --release .......................... Deploy a release build

    --noprepare ........................ Don't prepare
    --nobuild .......................... Don't build

    --device ........................... Deploy to a device
    --emulator ......................... Deploy to an emulator
    --target ........................... Deploy to a specific target

    --buildConfig....................... Use the specified build configuration
                                         instead of default build.json

To provide platform specific options, you must include them after `--`.

Technical details
    calls cordova prepare (unless --noprepare)
    calls PLATFORM run
        PLATFORM run calls PLATFORM build (unless --nobuild)

Examples
    cordova-cli run android --release --buildConfig=..\myBuildConfig.json --target=myEmulator
    cordova-cli run android --nobuild
    cordova-cli run ios --device
    cordova-cli run ios --list
