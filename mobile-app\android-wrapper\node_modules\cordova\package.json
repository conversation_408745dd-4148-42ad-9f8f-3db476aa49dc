{"name": "<PERSON><PERSON>", "version": "11.1.0", "description": "Cordova command line interface tool", "main": "<PERSON><PERSON>", "engines": {"node": ">=12.0.0"}, "bin": {"cordova": "./bin/cordova"}, "scripts": {"test": "npm run lint && npm run cover", "lint": "eslint . bin/cordova", "cover": "nyc jasmine"}, "repository": "github:apache/cordova-cli", "bugs": "https://github.com/apache/cordova-cli/issues", "keywords": ["<PERSON><PERSON>", "client", "cli"], "dependencies": {"configstore": "^5.0.1", "cordova-common": "^4.1.0", "cordova-create": "^4.1.0", "cordova-lib": "^11.1.0", "editor": "^1.0.0", "execa": "^5.1.1", "fs-extra": "^10.1.0", "insight": "^0.11.1", "loud-rejection": "^2.2.0", "nopt": "^5.0.0", "semver": "^7.3.8", "systeminformation": "^5.17.3", "update-notifier": "^5.1.0"}, "devDependencies": {"@cordova/eslint-config": "^4.0.0", "jasmine": "^3.99.0", "mock-stdin": "^1.0.0", "nyc": "^15.1.0", "rewire": "^5.0.0"}, "author": "Apache Software Foundation", "license": "Apache-2.0", "nyc": {"include": ["bin/**", "src/**"], "reporter": ["lcov", "text"]}}