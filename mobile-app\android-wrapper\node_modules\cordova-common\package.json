{"author": "Apache Software Foundation", "name": "cordova-common", "description": "Apache Cordova tools and platforms shared routines", "license": "Apache-2.0", "version": "4.1.0", "repository": "github:apache/cordova-common", "bugs": "https://github.com/apache/cordova-common/issues", "main": "cordova-common.js", "engines": {"node": ">=10.0.0"}, "scripts": {"test": "npm run lint && npm run cover", "test:unit": "jasmine \"spec/**/*.spec.js\"", "lint": "eslint .", "cover": "nyc npm run test:unit"}, "dependencies": {"@netflix/nerror": "^1.1.3", "ansi": "^0.3.1", "bplist-parser": "^0.2.0", "cross-spawn": "^7.0.1", "elementtree": "^0.1.7", "endent": "^1.4.1", "fast-glob": "^3.2.2", "fs-extra": "^9.0.0", "glob": "^7.1.6", "plist": "^3.0.1", "q": "^1.5.1", "read-chunk": "^3.2.0", "strip-bom": "^4.0.0", "underscore": "^1.9.2"}, "devDependencies": {"@cordova/eslint-config": "^3.0.0", "@nodelib/fs.macchiato": "^1.0.2", "jasmine": "^3.5.0", "jasmine-spec-reporter": "^5.0.1", "nyc": "^15.1.0", "rewire": "^5.0.0"}, "nyc": {"all": true, "exclude": ["coverage/", "spec/"], "reporter": ["lcov", "text"]}}