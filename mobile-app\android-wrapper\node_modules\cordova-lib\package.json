{"author": "Apache Software Foundation", "name": "cordova-lib", "license": "Apache-2.0", "description": "Apache Cordova tools core lib and API", "version": "11.1.0", "repository": "github:apache/cordova-lib", "bugs": "https://github.com/apache/cordova-lib/issues", "main": "cordova-lib.js", "engines": {"node": ">=12"}, "dependencies": {"cordova-common": "^4.1.0", "cordova-fetch": "^3.1.0", "cordova-serve": "^4.0.0", "dep-graph": "^1.1.0", "detect-indent": "^6.1.0", "detect-newline": "^3.1.0", "elementtree": "^0.1.7", "execa": "^5.1.1", "fs-extra": "^10.1.0", "globby": "^11.1.0", "init-package-json": "^2.0.5", "md5-file": "^5.0.0", "pify": "^5.0.0", "semver": "^7.3.8", "stringify-package": "^1.0.1", "write-file-atomic": "^3.0.3"}, "devDependencies": {"@cordova/eslint-config": "^4.0.0", "cordova-android": "10.1.2", "delay": "^5.0.0", "jasmine": "^3.99.0", "jasmine-spec-reporter": "^5.0.2", "nyc": "^15.1.0", "rewire": "^5.0.0"}, "scripts": {"test": "npm run lint && npm run test:coverage", "test:unit": "jasmine \"spec/**/*.spec.js\"", "test:e2e": "jasmine \"integration-tests/**/*.spec.js\"", "test:all": "npm run test:unit && npm run test:e2e", "test:coverage": "nyc npm run test:all", "lint": "eslint ."}, "nyc": {"all": true, "exclude": ["templates/", "integration-tests/", "coverage/", "spec/"], "reporter": ["lcov", "text"]}}