<a name="1.1.3"></a>
### 1.1.3 (2019-11-27)


#### Bug Fixes

* **nerror:**  don't call extsprintf if single string passed to VError constructor (#11) ([89b2088b](https://github.com/Netflix/nerror/commit/89b2088b))


<a name="1.1.2"></a>
### 1.1.2 (2019-07-26)


<a name="1.1.1"></a>
### 1.1.1 (2019-07-09)


<a name="1.1.0"></a>
## 1.1.0 (2019-05-06)


#### Features

* **errorForEach:** make it MultiError support more general (#2) ([96cc8769](https://github.com/Netflix/nerror/commit/96cc8769))


<a name="1.0.0"></a>
## 1.0.0 (2019-05-02)


#### Features

* **nerror:** initial commit ([c959d4fc](https://github.com/Netflix/nerror/commit/c959d4fc))


#### Breaking Changes

* initial release

 ([6ae8bd30](https://github.com/Netflix/nerror/commit/6ae8bd30))

