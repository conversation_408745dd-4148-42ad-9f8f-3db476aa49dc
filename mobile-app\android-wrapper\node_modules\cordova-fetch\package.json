{"name": "cordova-fetch", "version": "3.1.0", "description": "Apache Cordova fetch module. Fetches from git and npm.", "main": "index.js", "repository": "github:apache/cordova-fetch", "bugs": "https://github.com/apache/cordova-fetch/issues", "keywords": ["<PERSON><PERSON>", "fetch", "apache", "ecosystem:cordova", "cordova:tool"], "author": "Apache Software Foundation", "license": "Apache-2.0", "dependencies": {"cordova-common": "^4.1.0", "fs-extra": "^9.1.0", "npm-package-arg": "^8.1.5", "pacote": "^11.3.5", "pify": "^5.0.0", "resolve": "^1.22.1", "semver": "^7.3.8", "which": "^2.0.2"}, "devDependencies": {"@cordova/eslint-config": "^3.0.0", "file-url": "^3.0.0", "jasmine": "^3.99.0", "nyc": "^15.1.0", "rewire": "^5.0.0"}, "scripts": {"test": "npm run lint && npm run test:coverage", "test:coverage": "nyc jasmine", "lint": "eslint --ignore-path .gitignore ."}, "engines": {"node": ">= 10", "npm": ">= 5.6.0"}, "nyc": {"include": ["index.js"], "reporter": ["lcov", "text"]}}