Synopsis

    cordova-cli build [PROD] [TARGET] [PLATS] [BUILD<PERSON>NFIG] [-- POPTS]

      PROD:   --debug|--release
      TARGET: --device|--emulator
      PLATS:  PLATFORM [...]
      BUILDCONFIG: --buildConfig=CONFIGFILE
      POPTS:  platformopts

Shortcut for `cordova prepare` + `cordova compile` for
all/the specified platforms.

    --debug ............................ debug build
    --release .......................... release build

    --device ........................... Build it for a device
    --emulator ......................... Build it for an emulator

    --buildConfig....................... Use the specified build configuration
                                         instead of default build.json

To provide platform specific options, you must include them after `--`.

Example
    cordova-cli build android windows --debug --device
    cordova-cli build android --release --buildConfig=..\myBuildConfig.json
    cordova-cli build android --release -- --keystore="..\android.keystore" --storePassword=android --alias=mykey
