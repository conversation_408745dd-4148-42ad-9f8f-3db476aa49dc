{"name": "@netflix/nerror", "version": "1.1.3", "main": "lib/index.js", "description": "Rich errors", "homepage": "https://github.com/Netflix/nerror", "repository": {"type": "git", "url": "https://github.com/Netflix/nerror"}, "license": "MIT", "files": ["lib", "lib/index.d.ts"], "types": "lib/index.d.ts", "keywords": ["nerror", "error", "multierror", "verror"], "devDependencies": {"chai": "^4.1.2", "conventional-changelog-angular": "^5.0.0", "conventional-recommended-bump": "^4.0.0", "documentation": "^8.0.0", "eslint": "^4.19.1", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.0", "mocha": "^5.2.0", "nyc": "^12.0.2", "prettier": "^1.13.5", "tsd": "^0.7.3", "unleash": "^2.0.1"}, "dependencies": {"assert-plus": "^1.0.0", "extsprintf": "^1.4.0", "lodash": "^4.17.15"}, "scripts": {"test": "make prepush & tsd"}, "tsd": {"directory": "test"}}