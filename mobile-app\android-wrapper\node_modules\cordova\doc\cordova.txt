Synopsis

    cordova-cli command [options]

Global Commands
    create ............................. Create a project
    help ............................... Get help for a command
    telemetry .......................... Turn telemetry collection on or off
    config ............................. Set, get, delete, edit, and list global cordova options

Project Commands
    info ............................... Generate project information
    requirements ....................... Checks and print out all the requirements
                                            for platforms specified

    platform ........................... Manage project platforms
    plugin ............................. Manage project plugins

    prepare ............................ Copy files into platform(s) for building
    compile ............................ Build platform(s)
    clean .............................. Cleanup project from build artifacts

    run ................................ Run project
                                            (including prepare && compile)
    serve .............................. Run project with a local webserver
                                            (including prepare)

Learn more about command options using 'cordova-cli help <command>'

Aliases
    build -> cordova-cli prepare && cordova-cli compile
    emulate -> cordova-cli run --emulator

Options
    -v, --version ...................... prints out this utility's version
    -d, --verbose ...................... debug mode produces verbose log output for all activity,
    --no-update-notifier ............... disables check for CLI updates
    --nohooks .......................... suppress executing hooks
                                         (taking RegExp hook patterns as parameters)

Examples
    cordova-cli create myApp org.apache.cordova.myApp myApp
    cordova-cli plugin add cordova-plugin-camera
    cordova-cli platform add android
    cordova-cli plugin add cordova-plugin-camera --nosave
    cordova-cli platform add android --nosave
    cordova-cli requirements android    
    cordova-cli build android --verbose
    cordova-cli run android
    cordova-cli build android --release -- --keystore="..\android.keystore" --storePassword=android --alias=mykey
    cordova-cli config ls
