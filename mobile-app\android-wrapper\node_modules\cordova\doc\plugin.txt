Synopsis

    cordova-cli plugin <command> [options]

Manage project plugins

    add <plugin-spec> [...] ............ Add specified plugins and save them to
                                         config.xml & package.json.
        --searchpath <directory> ....... When looking up plugins by ID, look in this directory and
                                         each of its subdirectories before hitting the registry.
                                         Multiple search paths can be specified.

        --noregistry ................... Don't search the registry for plugins

        --link ......................... When installing from a local path, creates a symbolic link
                                         instead of copying files. The extent to which files are linked
                                         varies by platform. Useful for plugin development.
        --nosave ....................... Prevent saving the information for specified plugin
                                         into config.xml & package.json.
        --shrinkwrap ................... Used together with --save, saves the
                                         installed version numbers to config.xml

        --force ........................ Forces copying source files from the plugin even if the
                                         same file already exists in the target directory.


    remove <pluginid>|<name> [...] ..... Remove plugins with the given IDs/name and
                                         removes the information for specified plugin from config.xml & package.json.
        --nosave ....................... Prevents removing the information for
                                         specified plugin from config.xml & package.json.


    list .............................. List currently installed plugins

Syntax
    <plugin-spec> : <pluginID>[@<version>]|<directory>|<url>[#<commit-ish>][:subdir]

    <plugin> .......................... Plugin id (id of plugin in npm registry or --searchPath)
    <version> ......................... Major.minor.patch version specifier using semver
    <directory> ....................... Directory containing plugin.xml
    <url> ............................. Url to a git repository containing a plugin.xml
    <commit-ish> ...................... Commit/tag/branch reference. If none is specified, 'master' is used
    <subdir> .......................... Sub-directory to find plugin.xml for the specified plugin.

Aliases
    plugins -> plugin
    rm -> remove
    ls -> list

Examples
    cordova-cli plugin add cordova-plugin-camera cordova-plugin-file --nosave --searchpath ~/plugins
    cordova-cli plugin add cordova-plugin-camera@^2.0.0 --nosave
    cordova-cli plugin add https://github.com/myfork/cordova-plugin-camera.git#2.1.0 --nosave
    cordova-cli plugin add ../cordova-plugin-camera --nosave
    cordova-cli plugin add ../cordova-plugin-camera.tgz --nosave
    cordova-cli plugin rm camera --nosave
    cordova-cli plugin ls
