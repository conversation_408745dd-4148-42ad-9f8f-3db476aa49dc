                   __                                                         
                  /\ \                                                         __           
 __  __    ___    \_\ \     __   _ __   ____    ___    ___   _ __    __       /\_\    ____  
/\ \/\ \ /' _ `\  /'_  \  /'__`\/\  __\/ ,__\  / ___\ / __`\/\  __\/'__`\     \/\ \  /',__\ 
\ \ \_\ \/\ \/\ \/\ \ \ \/\  __/\ \ \//\__, `\/\ \__//\ \ \ \ \ \//\  __/  __  \ \ \/\__, `\
 \ \____/\ \_\ \_\ \___,_\ \____\\ \_\\/\____/\ \____\ \____/\ \_\\ \____\/\_\ _\ \ \/\____/
  \/___/  \/_/\/_/\/__,_ /\/____/ \/_/ \/___/  \/____/\/___/  \/_/ \/____/\/_//\ \_\ \/___/ 
                                                                              \ \____/      
                                                                               \/___/

Underscore is a utility-belt library for JavaScript that provides 
support for the usual functional suspects (each, map, reduce, filter...) 
without extending any core JavaScript objects.

For Docs, License, Tests, and pre-packed downloads, see:
http://documentcloud.github.com/underscore/

Many thanks to our contributors:
https://github.com/documentcloud/underscore/contributors
